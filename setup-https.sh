#!/bin/bash

# Laravel Docker HTTPS Setup Script
# This script sets up HTTPS support for the Laravel application in Docker

echo "🔧 Setting up HTTPS support for Laravel Docker environment..."

# Create SSL directory if it doesn't exist
if [ ! -d "docker/nginx/ssl" ]; then
    echo "📁 Creating SSL directory..."
    mkdir -p docker/nginx/ssl
fi

# Generate SSL certificates if they don't exist
if [ ! -f "docker/nginx/ssl/nginx.crt" ] || [ ! -f "docker/nginx/ssl/nginx.key" ]; then
    echo "🔐 Generating self-signed SSL certificates..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout docker/nginx/ssl/nginx.key \
        -out docker/nginx/ssl/nginx.crt \
        -subj "/C=US/ST=Local/L=Local/O=Development/OU=IT Department/CN=localhost"
    echo "✅ SSL certificates generated successfully!"
else
    echo "✅ SSL certificates already exist!"
fi

# Check if .env file exists, if not copy from .env.example
if [ ! -f ".env" ]; then
    echo "📄 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created with PostgreSQL configuration!"
else
    echo "✅ .env file already exists!"
    echo "🔍 Checking database configuration..."

    # Check if database is configured for PostgreSQL
    if grep -q "DB_CONNECTION=mysql" .env; then
        echo "⚠️  Updating database configuration from MySQL to PostgreSQL..."
        sed -i '' 's/DB_CONNECTION=mysql/DB_CONNECTION=pgsql/' .env
        sed -i '' 's/DB_HOST=127.0.0.1/DB_HOST=db/' .env
        sed -i '' 's/DB_PORT=3306/DB_PORT=5432/' .env
        sed -i '' 's/DB_DATABASE=infbf/DB_DATABASE=laravel/' .env
        sed -i '' 's/DB_USERNAME=root/DB_USERNAME=forge/' .env
        sed -i '' 's/DB_PASSWORD=/DB_PASSWORD=secret/' .env
        echo "✅ Database configuration updated to PostgreSQL!"
    else
        echo "✅ Database already configured for PostgreSQL!"
    fi
fi

# Build and start Docker containers
echo "🐳 Building and starting Docker containers..."
docker-compose up -d --build

# Wait for containers to be ready
echo "⏳ Waiting for containers to be ready..."
sleep 10

# Install Composer dependencies
echo "📦 Installing Composer dependencies..."
docker-compose exec app composer install

# Generate application key if not set
echo "🔑 Generating application key..."
docker-compose exec app php artisan key:generate

# Run database migrations
echo "🗄️  Running database migrations..."
docker-compose exec app php artisan migrate

echo ""
echo "🎉 HTTPS setup completed successfully!"
echo ""
echo "📋 Access URLs:"
echo "   HTTP:  http://localhost:8000"
echo "   HTTPS: https://localhost:8443"
echo ""
echo "⚠️  Note: You may see a security warning for the self-signed certificate."
echo "   This is normal for local development. Click 'Advanced' and 'Proceed to localhost'."
echo ""
echo "🔧 Available Docker commands:"
echo "   Start:   docker-compose up -d"
echo "   Stop:    docker-compose down"
echo "   Logs:    docker-compose logs -f"
echo "   Shell:   docker-compose exec app bash"
echo ""
