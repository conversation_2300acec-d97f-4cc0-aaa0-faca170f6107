#!/bin/bash

# Laravel Docker HTTPS Verification Script
# This script verifies that both HTTP and HTTPS are working correctly

echo "🔍 Verifying Laravel Docker HTTPS setup..."
echo ""

# Check if containers are running
echo "📦 Checking Docker containers..."
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ Docker containers are not running. Please run 'docker-compose up -d' first."
    exit 1
fi
echo "✅ Docker containers are running"
echo ""

# Test HTTP endpoint
echo "🌐 Testing HTTP endpoint (localhost:8000)..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000)
if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ HTTP endpoint is working (Status: $HTTP_STATUS)"
else
    echo "❌ HTTP endpoint failed (Status: $HTTP_STATUS)"
fi
echo ""

# Test HTTPS endpoint
echo "🔒 Testing HTTPS endpoint (localhost:8443)..."
HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -k https://localhost:8443)
if [ "$HTTPS_STATUS" = "200" ]; then
    echo "✅ HTTPS endpoint is working (Status: $HTTPS_STATUS)"
else
    echo "❌ HTTPS endpoint failed (Status: $HTTPS_STATUS)"
fi
echo ""

# Test database connection
echo "🗄️  Testing database connection..."
DB_TEST=$(docker-compose exec -T app php artisan tinker --execute="echo DB::connection()->getPdo() ? 'OK' : 'FAIL';" 2>/dev/null | grep -o "OK\|FAIL")
if [ "$DB_TEST" = "OK" ]; then
    echo "✅ Database connection is working"
else
    echo "❌ Database connection failed"
fi
echo ""

# Check SSL certificate
echo "🔐 Checking SSL certificate..."
if [ -f "docker/nginx/ssl/nginx.crt" ] && [ -f "docker/nginx/ssl/nginx.key" ]; then
    echo "✅ SSL certificates exist"
    
    # Check certificate validity
    CERT_VALID=$(openssl x509 -in docker/nginx/ssl/nginx.crt -noout -checkend 86400 2>/dev/null && echo "VALID" || echo "INVALID")
    if [ "$CERT_VALID" = "VALID" ]; then
        echo "✅ SSL certificate is valid"
    else
        echo "⚠️  SSL certificate may be expired or invalid"
    fi
else
    echo "❌ SSL certificates not found"
fi
echo ""

# Check Laravel configuration
echo "⚙️  Checking Laravel configuration..."
APP_KEY=$(docker-compose exec -T app php artisan tinker --execute="echo config('app.key') ? 'SET' : 'NOT_SET';" 2>/dev/null | grep -o "SET\|NOT_SET")
if [ "$APP_KEY" = "SET" ]; then
    echo "✅ Laravel application key is set"
else
    echo "❌ Laravel application key is not set. Run: docker-compose exec app php artisan key:generate"
fi
echo ""

# Summary
echo "📋 Setup Summary:"
echo "   HTTP URL:  http://localhost:8000"
echo "   HTTPS URL: https://localhost:8443"
echo ""

if [ "$HTTP_STATUS" = "200" ] && [ "$HTTPS_STATUS" = "200" ] && [ "$DB_TEST" = "OK" ]; then
    echo "🎉 All tests passed! Your Laravel HTTPS setup is working correctly."
    echo ""
    echo "🚀 You can now:"
    echo "   • Access your application via HTTP or HTTPS"
    echo "   • Use Laravel Sanctum for API authentication"
    echo "   • Develop with SSL/TLS encryption"
    echo "   • Use the IntelliJ IDEA run configurations"
else
    echo "⚠️  Some tests failed. Please check the output above and troubleshoot accordingly."
fi
echo ""
